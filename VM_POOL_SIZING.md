# VM 渲染池大小配置指南

## 🎯 为什么需要渲染池？

SSR 渲染池使用多个 VM 实例来并行处理请求，提高系统的并发处理能力和响应速度。

## 📊 默认配置：4个VM实例

### 选择4个实例的原因：

1. **CPU 核心匹配**
   - 现代机器通常有 4-8 个 CPU 核心
   - 4个VM实例可以充分利用多核并行处理
   - 避免过度订阅导致上下文切换开销

2. **内存使用合理**
   - 每个VM上下文约占用 10-20MB 内存
   - 4个实例总计 40-80MB，在可接受范围内
   - 为其他应用程序留出足够内存空间

3. **并发能力适中**
   - 可同时处理4个SSR请求
   - 对大多数中小型应用足够
   - 避免资源竞争和性能下降

4. **启动时间平衡**
   - VM上下文创建需要时间
   - 4个实例在启动速度和并发能力间平衡

## 🔧 自定义池大小

### 方法1：环境变量
```bash
# 设置为8个VM实例
export SSR_POOL_SIZE=8
npm run start:ssr

# 或者直接运行
SSR_POOL_SIZE=6 npm run start:ssr
```

### 方法2：智能自动计算
系统会根据硬件配置自动计算最优池大小：

```javascript
function calculateOptimalPoolSize() {
  const cpuCount = os.cpus().length;
  const totalMemoryGB = os.totalmem() / (1024 * 1024 * 1024);
  
  // 基于CPU和内存计算
  let poolSize = Math.min(cpuCount, Math.floor(totalMemoryGB / 2));
  
  // 限制在合理范围内
  return Math.max(2, Math.min(poolSize, 8));
}
```

## 📈 不同场景的推荐配置

### 🏠 开发环境
```bash
SSR_POOL_SIZE=2  # 节省资源，便于调试
```
- **优点**: 资源占用少，启动快
- **缺点**: 并发能力有限
- **适用**: 本地开发、调试

### 🏢 生产环境 - 小型应用
```bash
SSR_POOL_SIZE=4  # 默认配置
```
- **优点**: 平衡性能和资源使用
- **缺点**: 高并发时可能不足
- **适用**: 日PV < 10万的应用

### 🏭 生产环境 - 中型应用
```bash
SSR_POOL_SIZE=6
```
- **优点**: 更好的并发处理能力
- **缺点**: 内存占用增加
- **适用**: 日PV 10-50万的应用

### 🌐 生产环境 - 大型应用
```bash
SSR_POOL_SIZE=8
```
- **优点**: 最大并发处理能力
- **缺点**: 资源占用最高
- **适用**: 日PV > 50万的应用

## 🔍 性能监控指标

### 关键指标
1. **渲染时间**: 单次渲染耗时
2. **队列等待**: 请求等待VM可用的时间
3. **内存使用**: VM实例的内存占用
4. **CPU使用率**: 系统CPU负载

### 监控命令
```bash
# 查看渲染器状态
curl http://localhost:3091/renderer-status

# 查看系统资源
curl http://localhost:3091/health
```

## ⚖️ 池大小选择决策树

```
开始
├── 是开发环境？
│   ├── 是 → 使用 2 个实例
│   └── 否 → 继续
├── CPU核心数 < 4？
│   ├── 是 → 使用 2 个实例
│   └── 否 → 继续
├── 内存 < 4GB？
│   ├── 是 → 使用 2-3 个实例
│   └── 否 → 继续
├── 预期并发 < 10？
│   ├── 是 → 使用 4 个实例
│   └── 否 → 继续
├── 预期并发 < 50？
│   ├── 是 → 使用 6 个实例
│   └── 否 → 使用 8 个实例
```

## 🚨 注意事项

### 过少的实例（1-2个）
- **问题**: 并发瓶颈，请求排队
- **现象**: 响应时间增加，用户体验差
- **解决**: 增加实例数量

### 过多的实例（10+个）
- **问题**: 内存占用过高，上下文切换频繁
- **现象**: 系统负载高，可能出现OOM
- **解决**: 减少实例数量或增加服务器资源

### 内存不足
- **问题**: VM实例创建失败
- **现象**: 启动时报错，渲染失败
- **解决**: 减少池大小或增加内存

## 📊 性能测试结果

基于不同池大小的性能测试（模拟数据）：

| 池大小 | 并发请求 | 平均响应时间 | 内存占用 | CPU使用率 |
|--------|----------|--------------|----------|-----------|
| 2      | 10       | 150ms        | 40MB     | 60%       |
| 4      | 20       | 80ms         | 80MB     | 70%       |
| 6      | 30       | 60ms         | 120MB    | 80%       |
| 8      | 40       | 50ms         | 160MB    | 85%       |

## 🔧 动态调整

### 运行时监控
```javascript
// 监控渲染队列长度
if (queueLength > poolSize * 2) {
  console.warn('考虑增加池大小');
}

// 监控内存使用
if (memoryUsage > totalMemory * 0.8) {
  console.warn('考虑减少池大小');
}
```

### 自动扩缩容（高级）
```javascript
// 根据负载动态调整池大小
function autoScale() {
  const avgResponseTime = getAverageResponseTime();
  const memoryUsage = getMemoryUsage();
  
  if (avgResponseTime > 200 && memoryUsage < 0.7) {
    increasePoolSize();
  } else if (avgResponseTime < 50 && memoryUsage > 0.5) {
    decreasePoolSize();
  }
}
```

## 💡 最佳实践

1. **从小开始**: 先使用较小的池大小，根据实际负载调整
2. **监控为先**: 建立完善的监控体系
3. **压力测试**: 在生产环境前进行充分的压力测试
4. **资源预留**: 为系统其他组件预留足够资源
5. **文档记录**: 记录配置变更的原因和效果

## 🎯 总结

选择合适的VM池大小需要考虑：
- **硬件资源**: CPU核心数、内存大小
- **应用负载**: 预期并发数、响应时间要求
- **运行环境**: 开发、测试、生产环境的不同需求
- **监控反馈**: 基于实际运行数据进行调优

默认的4个实例是一个经过权衡的选择，适合大多数场景。根据具体需求，可以通过环境变量灵活调整。
