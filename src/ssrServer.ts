import http from 'node:http';
import fs from 'node:fs';
import path from 'node:path';
import vm from 'node:vm';
import { fileURLToPath } from 'url';
import { createRequire } from 'node:module';
import { renderToString } from 'react-dom/server';
import A from './components/a';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);

// 智能计算最优池大小
function calculateOptimalPoolSize(): number {
  const cpuCount = require('os').cpus().length;
  const totalMemoryGB = require('os').totalmem() / (1024 * 1024 * 1024);

  // 基于CPU核心数和内存大小计算
  let poolSize = Math.min(cpuCount, Math.floor(totalMemoryGB / 2)); // 每2GB内存分配1个VM

  // 设置合理的范围
  poolSize = Math.max(2, Math.min(poolSize, 8)); // 最少2个，最多8个

  console.log(`系统信息: ${cpuCount} CPU核心, ${totalMemoryGB.toFixed(1)}GB 内存`);
  console.log(`建议池大小: ${poolSize}`);

  return poolSize;
}

interface RenderContext {
  user?: string;
  userAgent?: string;
  cookies?: Record<string, string>;
  query?: Record<string, string>;
  headers?: Record<string, string>;
}

interface RenderResult {
  html: string;
  status: number;
  headers: Record<string, string>;
}

// 基于 VM 的 SSR 渲染器
class SSRRenderer {
  private contexts: vm.Context[] = [];
  private readonly poolSize: number;
  private currentIndex = 0;

  constructor(poolSize = 4) {
    this.poolSize = poolSize;
  }

  async initialize() {
    console.log('初始化 SSR 渲染器...');

    try {
      // 读取编译后的服务器端代码
      const serverModulePath = path.join(__dirname, '../dist/entry-server.js');

      if (!fs.existsSync(serverModulePath)) {
        throw new Error(`服务器端代码未找到: ${serverModulePath}`);
      }

      const serverCode = fs.readFileSync(serverModulePath, 'utf8');
      console.log('服务器端代码已读取，大小:', serverCode.length, '字符');

      // 创建多个 VM 上下文（渲染池）
      for (let i = 0; i < this.poolSize; i++) {
        const context = await this.createVMContext(serverCode, i);
        this.contexts.push(context);
      }

      console.log(`SSR 渲染器初始化完成，创建了 ${this.contexts.length} 个 VM 上下文`);
    } catch (error) {
      console.error('初始化 SSR 渲染器失败:', error);
      throw error;
    }
  }

  private async createVMContext(serverCode: string, contextId: number): Promise<vm.Context> {
    console.log(`创建 VM 上下文 ${contextId + 1}...`);
    console.log(renderToString(<A />));

    // 创建沙箱环境
    const sandbox = {
      // 基础 JavaScript 对象
      console: {
        log: (...args: any[]) => console.log(`[VM-${contextId}]`, ...args),
        error: (...args: any[]) => console.error(`[VM-${contextId}]`, ...args),
        warn: (...args: any[]) => console.warn(`[VM-${contextId}]`, ...args),
        info: (...args: any[]) => console.info(`[VM-${contextId}]`, ...args),
      },

      // 时间相关
      Date: Date,
      setTimeout: setTimeout,
      clearTimeout: clearTimeout,
      setInterval: setInterval,
      clearInterval: clearInterval,

      // 异步处理
      Promise: Promise,

      // 数学和工具
      Math: Math,
      JSON: JSON,

      // Node.js 相关
      Buffer: Buffer,
      process: {
        env: process.env,
        version: process.version,
        platform: process.platform,
        arch: process.arch,
      },

      // 模块系统
      require: require,
      module: { exports: {} },
      exports: {},
      __dirname: __dirname,
      __filename: path.join(__dirname, 'entry-server.js'),

      // 全局引用
      global: undefined as any,

      // 自定义渲染函数存储
      __ssrRender: null,
    };

    // 设置 global 引用自身
    sandbox.global = sandbox;

    // 创建 VM 上下文
    const context = vm.createContext(sandbox, {
      name: `SSR-Context-${contextId}`,
      codeGeneration: {
        strings: false,
        wasm: false,
      },
    });

    try {
      // 将 ES6 模块转换为 CommonJS 并包装代码
      let processedCode = serverCode;

      // 将 export 语句转换为 CommonJS
      processedCode = processedCode.replace(/export\s+async\s+function\s+(\w+)/g, 'async function $1');
      processedCode = processedCode.replace(/export\s+function\s+(\w+)/g, 'function $1');
      processedCode = processedCode.replace(/export\s+const\s+(\w+)/g, 'const $1');
      processedCode = processedCode.replace(/export\s+let\s+(\w+)/g, 'let $1');
      processedCode = processedCode.replace(/export\s+var\s+(\w+)/g, 'var $1');
      processedCode = processedCode.replace(/export\s+\{([^}]+)\}/g, '');

      // 移除 import 语句（在沙箱中不需要）
      processedCode = processedCode.replace(/import\s+.*?from\s+['"][^'"]+['"];?\s*/g, '');
      processedCode = processedCode.replace(/import\s+['"][^'"]+['"];?\s*/g, '');

      const wrappedCode = `
        ${processedCode}

        // 确保 render 函数可以被访问
        if (typeof render !== 'undefined') {
          __ssrRender = render;
          module.exports = { render };
        } else {
          throw new Error('未找到 render 函数');
        }
      `;

      // 在 VM 上下文中执行代码
      vm.runInContext(wrappedCode, context, {
        filename: 'entry-server.js',
        timeout: 10000,
        displayErrors: true,
      });

      // 验证 render 函数是否可用
      const renderFunction = vm.runInContext('__ssrRender', context);
      if (typeof renderFunction !== 'function') {
        throw new Error('render 函数未正确导出');
      }

      console.log(`VM 上下文 ${contextId + 1} 创建成功`);
      return context;
    } catch (error) {
      console.error(`创建 VM 上下文 ${contextId + 1} 失败:`, error);
      throw error;
    }
  }

  async render(url: string, ctx: RenderContext): Promise<RenderResult> {
    if (this.contexts.length === 0) {
      throw new Error('SSR 渲染器未初始化');
    }

    // 轮询选择一个 VM 上下文
    const context = this.contexts[this.currentIndex++ % this.contexts.length];
    const contextId = this.currentIndex % this.contexts.length;

    try {
      // 在 VM 上下文中执行渲染
      const renderScript = `
        (async function() {
          try {
            const renderFn = __ssrRender;
            if (typeof renderFn !== 'function') {
              throw new Error('render 函数不可用');
            }

            const url = ${JSON.stringify(url)};
            const ctx = ${JSON.stringify(ctx)};

            return await renderFn(url, ctx);
          } catch (error) {
            throw new Error('渲染执行错误: ' + error.message);
          }
        })()
      `;

      const result = await vm.runInContext(renderScript, context, {
        timeout: 15000,
        breakOnSigint: true,
        displayErrors: true,
      });

      console.log(`[VM-${contextId}] 渲染完成: ${url}`);
      return result;
    } catch (error) {
      console.error(`[VM-${contextId}] SSR 渲染错误:`, error);

      // 如果是超时错误，提供更详细的信息
      if (error instanceof Error && error.message.includes('timeout')) {
        throw new Error(`渲染超时: ${url} (超过 15 秒)`);
      }

      throw error;
    }
  }

  // 获取渲染器状态
  getStatus() {
    return {
      poolSize: this.poolSize,
      contextsCreated: this.contexts.length,
      currentIndex: this.currentIndex,
      totalRenders: this.currentIndex,
    };
  }

  // 清理资源
  destroy() {
    console.log('清理 SSR 渲染器资源...');
    this.contexts = [];
    console.log('SSR 渲染器资源已清理');
  }
}

// 解析 cookies
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};
  if (cookieHeader) {
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }
  return cookies;
}

// 解析查询参数
function parseQuery(url: string): Record<string, string> {
  const query: Record<string, string> = {};
  const queryString = url.split('?')[1];
  if (queryString) {
    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=');
      if (key && value) {
        query[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    });
  }
  return query;
}

// 创建 SSR 服务器
async function createSSRServer() {
  console.log('启动 SSR 服务器...');

  // 初始化渲染器（可根据需求调整池大小）
  const poolSize = process.env.SSR_POOL_SIZE
    ? parseInt(process.env.SSR_POOL_SIZE)
    : calculateOptimalPoolSize();
  console.log(`使用 ${poolSize} 个 VM 上下文`);

  const renderer = new SSRRenderer(poolSize);
  await renderer.initialize();

  const server = http.createServer(async (req, res) => {
    const startTime = Date.now();
    const url = req.url || '/';
    const pathname = url.split('?')[0];

    console.log(`[${new Date().toISOString()}] ${req.method} ${url}`);

    try {
      // 静态资源处理
      if (pathname.startsWith('/static/') || pathname.includes('.')) {
        res.writeHead(404, { 'content-type': 'text/plain' });
        res.end('Static files not implemented');
        return;
      }

      // 健康检查
      if (pathname === '/health') {
        res.writeHead(200, { 'content-type': 'application/json' });
        res.end(JSON.stringify({
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          renderer: renderer.getStatus()
        }));
        return;
      }

      // 渲染器状态
      if (pathname === '/renderer-status') {
        res.writeHead(200, { 'content-type': 'application/json' });
        res.end(JSON.stringify({
          renderer: renderer.getStatus(),
          memory: process.memoryUsage(),
          timestamp: new Date().toISOString()
        }));
        return;
      }

      // 构建渲染上下文
      const renderContext: RenderContext = {
        user: 'Alice', // 这里可以从 session/JWT 中获取
        userAgent: req.headers['user-agent'],
        cookies: parseCookies(req.headers.cookie || ''),
        query: parseQuery(url),
        headers: req.headers as Record<string, string>
      };

      // 执行 SSR 渲染
      const result = await renderer.render(pathname, renderContext);

      // 添加性能头部
      const renderTime = Date.now() - startTime;
      result.headers['x-render-time-ms'] = renderTime.toString();
      result.headers['x-server-time'] = new Date().toISOString();

      // 返回结果
      res.writeHead(result.status, result.headers);
      res.end(result.html);

      console.log(`渲染完成: ${pathname} (${renderTime}ms)`);
    } catch (error) {
      console.error('服务器错误:', error);

      // 错误页面
      const errorHtml = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
          <meta charset="UTF-8">
          <title>服务器错误</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 40px; 
              background: #f5f5f5; 
            }
            .error-container { 
              max-width: 600px; 
              margin: 0 auto; 
              background: white; 
              padding: 30px; 
              border-radius: 8px; 
              box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            }
            .error { 
              background: #ffebee; 
              padding: 20px; 
              border-radius: 4px; 
              border-left: 4px solid #f44336; 
            }
            .error-details {
              background: #f5f5f5;
              padding: 15px;
              border-radius: 4px;
              margin-top: 15px;
              font-family: monospace;
              font-size: 14px;
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <div class="error">
              <h1>🚨 服务器错误</h1>
              <p>抱歉，服务器在处理您的请求时发生了错误。</p>
              <div class="error-details">
                <strong>错误信息:</strong> ${error instanceof Error ? error.message : '未知错误'}<br>
                <strong>请求路径:</strong> ${pathname}<br>
                <strong>时间:</strong> ${new Date().toISOString()}
              </div>
              <p><a href="/">返回首页</a></p>
            </div>
          </div>
        </body>
        </html>
      `;

      res.writeHead(500, { 'content-type': 'text/html; charset=utf-8' });
      res.end(errorHtml);
    }
  });

  return { server, renderer };
}

// 启动服务器
async function startServer() {
  try {
    const { server, renderer } = await createSSRServer();
    const PORT = process.env.SSR_PORT || 3091;

    server.listen(PORT, () => {
      console.log(`🚀 SSR 服务器运行在端口 ${PORT}`);
      console.log(`📱 访问 http://localhost:${PORT} 查看演示`);
      console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
      console.log(`📊 渲染器状态: http://localhost:${PORT}/renderer-status`);
      console.log('');
      console.log('可用路由:');
      console.log('  / - 首页');
      console.log('  /about - 关于我们');
      console.log('  /user/123 - 用户资料');
      console.log('  /products - 产品列表');
      console.log('  /products/1 - 产品详情');
      console.log('');
      console.log('VM 渲染池状态:', renderer.getStatus());
    });

    // 优雅关闭
    const gracefulShutdown = (signal: string) => {
      console.log(`收到 ${signal} 信号，正在关闭服务器...`);
      server.close(() => {
        console.log('HTTP 服务器已关闭');
        renderer.destroy();
        console.log('SSR 服务器已完全关闭');
        process.exit(0);
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('启动 SSR 服务器失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer();
}

export { createSSRServer, SSRRenderer };
