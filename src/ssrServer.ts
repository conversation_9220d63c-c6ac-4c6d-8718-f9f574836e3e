import http from 'node:http';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface RenderContext {
  user?: string;
  userAgent?: string;
  cookies?: Record<string, string>;
  query?: Record<string, string>;
  headers?: Record<string, string>;
}

interface RenderResult {
  html: string;
  status: number;
  headers: Record<string, string>;
}

// 简化的 SSR 渲染器
class SSRRenderer {
  private renderModule: any = null;

  async initialize() {
    console.log('初始化 SSR 渲染器...');

    try {
      // 动态导入编译后的服务器端代码
      const serverModulePath = path.join(__dirname, 'entry-server.js');

      if (!fs.existsSync(serverModulePath)) {
        throw new Error(`服务器端代码未找到: ${serverModulePath}`);
      }

      // 使用动态导入加载模块
      const moduleUrl = `file://${serverModulePath}`;
      this.renderModule = await import(moduleUrl);

      if (!this.renderModule.render) {
        throw new Error('render 函数未找到');
      }

      console.log('SSR 渲染器初始化完成');
    } catch (error) {
      console.error('初始化 SSR 渲染器失败:', error);
      throw error;
    }
  }

  async render(url: string, ctx: RenderContext): Promise<RenderResult> {
    if (!this.renderModule) {
      throw new Error('SSR 渲染器未初始化');
    }

    try {
      return await this.renderModule.render(url, ctx);
    } catch (error) {
      console.error('SSR 渲染错误:', error);
      throw error;
    }
  }
}

// 解析 cookies
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};
  if (cookieHeader) {
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }
  return cookies;
}

// 解析查询参数
function parseQuery(url: string): Record<string, string> {
  const query: Record<string, string> = {};
  const queryString = url.split('?')[1];
  if (queryString) {
    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=');
      if (key && value) {
        query[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    });
  }
  return query;
}

// 创建 SSR 服务器
async function createSSRServer() {
  console.log('启动 SSR 服务器...');

  // 初始化渲染器
  const renderer = new SSRRenderer();
  await renderer.initialize();

  const server = http.createServer(async (req, res) => {
    const startTime = Date.now();
    const url = req.url || '/';
    const pathname = url.split('?')[0];

    console.log(`[${new Date().toISOString()}] ${req.method} ${url}`);

    try {
      // 静态资源处理
      if (pathname.startsWith('/static/') || pathname.includes('.')) {
        res.writeHead(404, { 'content-type': 'text/plain' });
        res.end('Static files not implemented');
        return;
      }

      // 健康检查
      if (pathname === '/health') {
        res.writeHead(200, { 'content-type': 'application/json' });
        res.end(JSON.stringify({
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: process.uptime()
        }));
        return;
      }

      // 构建渲染上下文
      const renderContext: RenderContext = {
        user: 'Alice', // 这里可以从 session/JWT 中获取
        userAgent: req.headers['user-agent'],
        cookies: parseCookies(req.headers.cookie || ''),
        query: parseQuery(url),
        headers: req.headers as Record<string, string>
      };

      // 执行 SSR 渲染
      const result = await renderer.render(pathname, renderContext);

      // 添加性能头部
      const renderTime = Date.now() - startTime;
      result.headers['x-render-time-ms'] = renderTime.toString();
      result.headers['x-server-time'] = new Date().toISOString();

      // 返回结果
      res.writeHead(result.status, result.headers);
      res.end(result.html);

      console.log(`渲染完成: ${pathname} (${renderTime}ms)`);
    } catch (error) {
      console.error('服务器错误:', error);

      // 错误页面
      const errorHtml = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
          <meta charset="UTF-8">
          <title>服务器错误</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 40px; 
              background: #f5f5f5; 
            }
            .error-container { 
              max-width: 600px; 
              margin: 0 auto; 
              background: white; 
              padding: 30px; 
              border-radius: 8px; 
              box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            }
            .error { 
              background: #ffebee; 
              padding: 20px; 
              border-radius: 4px; 
              border-left: 4px solid #f44336; 
            }
            .error-details {
              background: #f5f5f5;
              padding: 15px;
              border-radius: 4px;
              margin-top: 15px;
              font-family: monospace;
              font-size: 14px;
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <div class="error">
              <h1>🚨 服务器错误</h1>
              <p>抱歉，服务器在处理您的请求时发生了错误。</p>
              <div class="error-details">
                <strong>错误信息:</strong> ${error instanceof Error ? error.message : '未知错误'}<br>
                <strong>请求路径:</strong> ${pathname}<br>
                <strong>时间:</strong> ${new Date().toISOString()}
              </div>
              <p><a href="/">返回首页</a></p>
            </div>
          </div>
        </body>
        </html>
      `;

      res.writeHead(500, { 'content-type': 'text/html; charset=utf-8' });
      res.end(errorHtml);
    }
  });

  return server;
}

// 启动服务器
async function startServer() {
  try {
    const server = await createSSRServer();
    const PORT = process.env.SSR_PORT || 3091;

    server.listen(PORT, () => {
      console.log(`🚀 SSR 服务器运行在端口 ${PORT}`);
      console.log(`📱 访问 http://localhost:${PORT} 查看演示`);
      console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
      console.log('');
      console.log('可用路由:');
      console.log('  / - 首页');
      console.log('  /about - 关于我们');
      console.log('  /user/123 - 用户资料');
      console.log('  /products - 产品列表');
      console.log('  /products/1 - 产品详情');
    });

    // 优雅关闭
    process.on('SIGTERM', () => {
      console.log('收到 SIGTERM 信号，正在关闭服务器...');
      server.close(() => {
        console.log('SSR 服务器已关闭');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('收到 SIGINT 信号，正在关闭服务器...');
      server.close(() => {
        console.log('SSR 服务器已关闭');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('启动 SSR 服务器失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，则启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer();
}

export { createSSRServer, SSRRenderer };
