// SSR 入口文件
export interface RenderContext {
  user?: string;
  userAgent?: string;
  cookies?: Record<string, string>;
  query?: Record<string, string>;
  headers?: Record<string, string>;
}

export interface RenderResult {
  html: string;
  status: number;
  headers: Record<string, string>;
}

// 模拟路由匹配
function matchRoute(url: string): { component: string; params: Record<string, string> } {
  const routes = [
    { path: '/', component: 'Home' },
    { path: '/about', component: 'About' },
    { path: '/user/:id', component: 'UserProfile' },
    { path: '/products', component: 'ProductList' },
    { path: '/products/:id', component: 'ProductDetail' },
  ];

  // 简单的路由匹配逻辑
  for (const route of routes) {
    if (route.path === url) {
      return { component: route.component, params: {} };
    }
    
    // 处理动态路由
    if (route.path.includes(':')) {
      const routeParts = route.path.split('/');
      const urlParts = url.split('/');
      
      if (routeParts.length === urlParts.length) {
        const params: Record<string, string> = {};
        let match = true;
        
        for (let i = 0; i < routeParts.length; i++) {
          if (routeParts[i].startsWith(':')) {
            params[routeParts[i].slice(1)] = urlParts[i];
          } else if (routeParts[i] !== urlParts[i]) {
            match = false;
            break;
          }
        }
        
        if (match) {
          return { component: route.component, params };
        }
      }
    }
  }
  
  return { component: 'NotFound', params: {} };
}

// 模拟数据获取
async function fetchData(component: string, params: Record<string, string>, ctx: RenderContext): Promise<any> {
  // 模拟异步数据获取
  await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
  
  switch (component) {
    case 'Home':
      return {
        title: '首页',
        message: '欢迎来到我们的网站',
        stats: {
          users: 1234,
          products: 567,
          orders: 890
        }
      };
      
    case 'About':
      return {
        title: '关于我们',
        content: '我们是一家专注于技术创新的公司',
        team: ['Alice', 'Bob', 'Charlie']
      };
      
    case 'UserProfile':
      return {
        title: '用户资料',
        user: {
          id: params.id,
          name: `用户${params.id}`,
          email: `user${params.id}@example.com`,
          joinDate: '2024-01-01'
        }
      };
      
    case 'ProductList':
      return {
        title: '产品列表',
        products: [
          { id: 1, name: '产品A', price: 99.99 },
          { id: 2, name: '产品B', price: 149.99 },
          { id: 3, name: '产品C', price: 199.99 }
        ]
      };
      
    case 'ProductDetail':
      return {
        title: '产品详情',
        product: {
          id: params.id,
          name: `产品${params.id}`,
          price: Math.floor(Math.random() * 500) + 50,
          description: `这是产品${params.id}的详细描述`,
          features: ['特性1', '特性2', '特性3']
        }
      };
      
    default:
      return {
        title: '页面未找到',
        message: '抱歉，您访问的页面不存在'
      };
  }
}

// 渲染组件
function renderComponent(component: string, data: any, ctx: RenderContext): string {
  const baseStyles = `
    <style>
      body { 
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 0; 
        padding: 20px; 
        background: #f5f5f5; 
      }
      .container { 
        max-width: 800px; 
        margin: 0 auto; 
        background: white; 
        padding: 30px; 
        border-radius: 8px; 
        box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
      }
      .header { 
        border-bottom: 2px solid #007cba; 
        padding-bottom: 20px; 
        margin-bottom: 30px; 
      }
      .user-info { 
        background: #e3f2fd; 
        padding: 15px; 
        border-radius: 4px; 
        margin-bottom: 20px; 
      }
      .stats { 
        display: flex; 
        gap: 20px; 
        margin: 20px 0; 
      }
      .stat-item { 
        background: #f0f0f0; 
        padding: 15px; 
        border-radius: 4px; 
        text-align: center; 
        flex: 1; 
      }
      .product-grid { 
        display: grid; 
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
        gap: 20px; 
        margin: 20px 0; 
      }
      .product-card { 
        border: 1px solid #ddd; 
        padding: 15px; 
        border-radius: 4px; 
      }
      .features { 
        list-style: none; 
        padding: 0; 
      }
      .features li { 
        background: #e8f5e8; 
        padding: 8px; 
        margin: 5px 0; 
        border-radius: 4px; 
      }
    </style>
  `;

  switch (component) {
    case 'Home':
      return `
        ${baseStyles}
        <div class="container">
          <div class="header">
            <h1>${data.title}</h1>
            <p>${data.message}</p>
          </div>
          <div class="user-info">
            <strong>当前用户:</strong> ${ctx.user || '游客'} | 
            <strong>访问时间:</strong> ${new Date().toLocaleString()}
          </div>
          <div class="stats">
            <div class="stat-item">
              <h3>${data.stats.users}</h3>
              <p>用户数</p>
            </div>
            <div class="stat-item">
              <h3>${data.stats.products}</h3>
              <p>产品数</p>
            </div>
            <div class="stat-item">
              <h3>${data.stats.orders}</h3>
              <p>订单数</p>
            </div>
          </div>
          <nav>
            <a href="/about">关于我们</a> | 
            <a href="/products">产品列表</a> | 
            <a href="/user/123">用户资料</a>
          </nav>
        </div>
      `;
      
    case 'About':
      return `
        ${baseStyles}
        <div class="container">
          <div class="header">
            <h1>${data.title}</h1>
          </div>
          <div class="user-info">
            <strong>当前用户:</strong> ${ctx.user || '游客'}
          </div>
          <p>${data.content}</p>
          <h3>团队成员:</h3>
          <ul>
            ${data.team.map((member: string) => `<li>${member}</li>`).join('')}
          </ul>
          <nav>
            <a href="/">返回首页</a> | 
            <a href="/products">产品列表</a>
          </nav>
        </div>
      `;
      
    case 'UserProfile':
      return `
        ${baseStyles}
        <div class="container">
          <div class="header">
            <h1>${data.title}</h1>
          </div>
          <div class="user-info">
            <strong>当前访问用户:</strong> ${ctx.user || '游客'}
          </div>
          <h2>用户信息</h2>
          <p><strong>ID:</strong> ${data.user.id}</p>
          <p><strong>姓名:</strong> ${data.user.name}</p>
          <p><strong>邮箱:</strong> ${data.user.email}</p>
          <p><strong>加入日期:</strong> ${data.user.joinDate}</p>
          <nav>
            <a href="/">返回首页</a> | 
            <a href="/about">关于我们</a>
          </nav>
        </div>
      `;
      
    case 'ProductList':
      return `
        ${baseStyles}
        <div class="container">
          <div class="header">
            <h1>${data.title}</h1>
          </div>
          <div class="user-info">
            <strong>当前用户:</strong> ${ctx.user || '游客'}
          </div>
          <div class="product-grid">
            ${data.products.map((product: any) => `
              <div class="product-card">
                <h3>${product.name}</h3>
                <p><strong>价格:</strong> ¥${product.price}</p>
                <a href="/products/${product.id}">查看详情</a>
              </div>
            `).join('')}
          </div>
          <nav>
            <a href="/">返回首页</a> | 
            <a href="/about">关于我们</a>
          </nav>
        </div>
      `;
      
    case 'ProductDetail':
      return `
        ${baseStyles}
        <div class="container">
          <div class="header">
            <h1>${data.title}</h1>
          </div>
          <div class="user-info">
            <strong>当前用户:</strong> ${ctx.user || '游客'}
          </div>
          <h2>${data.product.name}</h2>
          <p><strong>价格:</strong> ¥${data.product.price}</p>
          <p><strong>描述:</strong> ${data.product.description}</p>
          <h3>产品特性:</h3>
          <ul class="features">
            ${data.product.features.map((feature: string) => `<li>${feature}</li>`).join('')}
          </ul>
          <nav>
            <a href="/products">返回产品列表</a> | 
            <a href="/">返回首页</a>
          </nav>
        </div>
      `;
      
    default:
      return `
        ${baseStyles}
        <div class="container">
          <div class="header">
            <h1>${data.title}</h1>
          </div>
          <div class="user-info">
            <strong>当前用户:</strong> ${ctx.user || '游客'}
          </div>
          <p>${data.message}</p>
          <nav>
            <a href="/">返回首页</a>
          </nav>
        </div>
      `;
  }
}

// 主渲染函数
export async function render(url: string, ctx: RenderContext): Promise<RenderResult> {
  try {
    // 路由匹配
    const { component, params } = matchRoute(url);
    
    // 数据获取
    const data = await fetchData(component, params, ctx);
    
    // 渲染组件
    const componentHtml = renderComponent(component, data, ctx);
    
    // 生成完整的HTML
    const html = `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${data.title} - SSR Demo</title>
        <meta name="description" content="Server-Side Rendering Demo">
      </head>
      <body>
        ${componentHtml}
        <script>
          // 客户端水合脚本
          console.log('SSR页面已加载');
          console.log('渲染时间:', new Date().toISOString());
          console.log('用户信息:', ${JSON.stringify(ctx)});
        </script>
      </body>
      </html>
    `;

    return {
      html,
      status: component === 'NotFound' ? 404 : 200,
      headers: {
        'content-type': 'text/html; charset=utf-8',
        'x-rendered-by': 'ssr-server',
        'x-render-time': new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('SSR渲染错误:', error);
    
    const errorHtml = `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <title>服务器错误</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          .error { background: #ffebee; padding: 20px; border-radius: 4px; }
        </style>
      </head>
      <body>
        <div class="error">
          <h1>服务器渲染错误</h1>
          <p>抱歉，页面渲染时发生了错误。</p>
          <p><strong>错误信息:</strong> ${error instanceof Error ? error.message : '未知错误'}</p>
          <a href="/">返回首页</a>
        </div>
      </body>
      </html>
    `;

    return {
      html: errorHtml,
      status: 500,
      headers: {
        'content-type': 'text/html; charset=utf-8'
      }
    };
  }
}
