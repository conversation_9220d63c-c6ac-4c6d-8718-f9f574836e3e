import express from 'express';
import { createServer } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// 存储连接的客户端
const clients = new Set<WebSocket>();

// 静态文件服务
app.use(express.static(path.join(__dirname, '../public')));

// 提供一个简单的HTML页面用于测试
app.get('/', (_req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebSocket Demo</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .message-box { 
                border: 1px solid #ccc; 
                height: 300px; 
                overflow-y: auto; 
                padding: 10px; 
                margin: 20px 0; 
                background: #f9f9f9;
            }
            .input-group { margin: 10px 0; }
            input[type="text"] { 
                width: 300px; 
                padding: 8px; 
                margin-right: 10px; 
            }
            button { 
                padding: 8px 16px; 
                background: #007cba; 
                color: white; 
                border: none; 
                cursor: pointer; 
            }
            button:hover { background: #005a87; }
            .status { 
                padding: 10px; 
                margin: 10px 0; 
                border-radius: 4px; 
            }
            .connected { background: #d4edda; color: #155724; }
            .disconnected { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>WebSocket Demo</h1>
            <div id="status" class="status disconnected">未连接</div>
            
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="输入消息..." />
                <button onclick="sendMessage()">发送消息</button>
                <button onclick="connect()">连接</button>
                <button onclick="disconnect()">断开连接</button>
            </div>
            
            <div class="message-box" id="messages"></div>
            
            <div class="input-group">
                <button onclick="sendWeatherRequest()">获取天气信息</button>
                <button onclick="startHeartbeat()">开始心跳</button>
                <button onclick="stopHeartbeat()">停止心跳</button>
            </div>
        </div>

        <script>
            let ws = null;
            let heartbeatInterval = null;
            
            function addMessage(message, type = 'info') {
                const messages = document.getElementById('messages');
                const div = document.createElement('div');
                div.style.margin = '5px 0';
                div.style.padding = '5px';
                div.style.borderRadius = '3px';
                
                if (type === 'sent') {
                    div.style.background = '#e3f2fd';
                    div.style.textAlign = 'right';
                } else if (type === 'received') {
                    div.style.background = '#f3e5f5';
                } else if (type === 'error') {
                    div.style.background = '#ffebee';
                    div.style.color = '#c62828';
                }
                
                div.innerHTML = \`<small>\${new Date().toLocaleTimeString()}</small><br>\${message}\`;
                messages.appendChild(div);
                messages.scrollTop = messages.scrollHeight;
            }
            
            function updateStatus(connected) {
                const status = document.getElementById('status');
                if (connected) {
                    status.textContent = '已连接';
                    status.className = 'status connected';
                } else {
                    status.textContent = '未连接';
                    status.className = 'status disconnected';
                }
            }
            
            function connect() {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    addMessage('已经连接了', 'error');
                    return;
                }
                
                ws = new WebSocket('ws://localhost:3089');
                
                ws.onopen = function() {
                    updateStatus(true);
                    addMessage('WebSocket 连接已建立');
                };
                
                ws.onmessage = function(event) {
                    let data;
                    try {
                        data = JSON.parse(event.data);
                        addMessage(\`收到: \${JSON.stringify(data, null, 2)}\`, 'received');
                    } catch (e) {
                        addMessage(\`收到: \${event.data}\`, 'received');
                    }
                };
                
                ws.onclose = function() {
                    updateStatus(false);
                    addMessage('WebSocket 连接已关闭');
                    stopHeartbeat();
                };
                
                ws.onerror = function(error) {
                    addMessage(\`WebSocket 错误: \${error}\`, 'error');
                };
            }
            
            function disconnect() {
                if (ws) {
                    ws.close();
                    stopHeartbeat();
                }
            }
            
            function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                
                if (!message) {
                    addMessage('请输入消息', 'error');
                    return;
                }
                
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    addMessage('WebSocket 未连接', 'error');
                    return;
                }
                
                const data = {
                    type: 'message',
                    content: message,
                    timestamp: new Date().toISOString()
                };
                
                ws.send(JSON.stringify(data));
                addMessage(\`发送: \${message}\`, 'sent');
                input.value = '';
            }
            
            function sendWeatherRequest() {
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    addMessage('WebSocket 未连接', 'error');
                    return;
                }
                
                const data = {
                    type: 'weather',
                    city: 'Beijing',
                    timestamp: new Date().toISOString()
                };
                
                ws.send(JSON.stringify(data));
                addMessage('发送天气请求', 'sent');
            }
            
            function startHeartbeat() {
                if (heartbeatInterval) {
                    addMessage('心跳已经在运行', 'error');
                    return;
                }
                
                heartbeatInterval = setInterval(() => {
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        const data = {
                            type: 'ping',
                            timestamp: new Date().toISOString()
                        };
                        ws.send(JSON.stringify(data));
                        addMessage('发送心跳', 'sent');
                    }
                }, 5000);
                
                addMessage('心跳已启动 (每5秒)');
            }
            
            function stopHeartbeat() {
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                    heartbeatInterval = null;
                    addMessage('心跳已停止');
                }
            }
            
            // 回车发送消息
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // 页面加载时自动连接
            window.onload = function() {
                connect();
            };
        </script>
    </body>
    </html>
  `);
});

// WebSocket 连接处理
wss.on('connection', (ws: WebSocket, req) => {
  console.log('新的 WebSocket 连接:', req.socket.remoteAddress);
  clients.add(ws);

  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'welcome',
    message: '欢迎连接到 WebSocket 服务器!',
    timestamp: new Date().toISOString(),
    clientCount: clients.size
  }));

  // 处理消息
  ws.on('message', (data: Buffer) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('收到消息:', message);

      // 根据消息类型处理
      switch (message.type) {
        case 'ping':
          // 心跳响应
          ws.send(JSON.stringify({
            type: 'pong',
            timestamp: new Date().toISOString()
          }));
          break;

        case 'weather':
          // 模拟天气数据
          setTimeout(() => {
            ws.send(JSON.stringify({
              type: 'weather_response',
              city: message.city || 'Unknown',
              temperature: Math.floor(Math.random() * 30) + 5,
              condition: ['晴天', '多云', '小雨', '阴天'][Math.floor(Math.random() * 4)],
              humidity: Math.floor(Math.random() * 40) + 40,
              timestamp: new Date().toISOString()
            }));
          }, 1000);
          break;

        case 'message':
          // 广播消息给所有客户端
          const broadcastMessage = {
            type: 'broadcast',
            content: message.content,
            from: 'client',
            timestamp: new Date().toISOString()
          };
          
          clients.forEach(client => {
            if (client !== ws && client.readyState === WebSocket.OPEN) {
              client.send(JSON.stringify(broadcastMessage));
            }
          });
          
          // 回复确认
          ws.send(JSON.stringify({
            type: 'message_ack',
            message: '消息已广播',
            timestamp: new Date().toISOString()
          }));
          break;

        default:
          ws.send(JSON.stringify({
            type: 'error',
            message: '未知的消息类型',
            timestamp: new Date().toISOString()
          }));
      }
    } catch (error) {
      console.error('处理消息错误:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: '消息格式错误',
        timestamp: new Date().toISOString()
      }));
    }
  });

  // 连接关闭处理
  ws.on('close', () => {
    console.log('WebSocket 连接关闭');
    clients.delete(ws);
  });

  // 错误处理
  ws.on('error', (error) => {
    console.error('WebSocket 错误:', error);
    clients.delete(ws);
  });
});

// 定期发送服务器状态
setInterval(() => {
  const statusMessage = {
    type: 'server_status',
    clientCount: clients.size,
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  };

  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(statusMessage));
    }
  });
}, 30000); // 每30秒发送一次

const PORT = process.env.WS_PORT || 3089;
server.listen(PORT, () => {
  console.log(`WebSocket 服务器运行在端口 ${PORT}`);
  console.log(`访问 http://localhost:${PORT} 查看演示页面`);
});
