import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";

export function createServer(): McpServer {
  const server = new McpServer({
    name: "Weather MCP Server",
    version: "0.1.0",
  });

  server.tool(
    "weather get",
    "根据城市查询对应的天气",
    {
      city: z.string().describe("city name"),
    },
    async ({ city }) => {
      if (!city) {
        throw new Error("city name is required.");
      }

      const response = await fetch(`https://wttr.in/${city}?format=j1`);
      const weather = await response.json();

      // const weather = {
      //   city: city,
      //   temperature: Math.floor(Math.random() * 30),
      //   condition: "Sunny",
      // };

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(weather, null, 2),
          },
        ],
      };
    },
  );

  return server;
}
