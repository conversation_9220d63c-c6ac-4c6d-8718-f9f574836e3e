import express, { Request, Response } from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// 存储SSE连接
const sseClients = new Set<Response>();

// 中间件
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// 主页面
app.get('/', (_req: Request, res: Response) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>Server-Sent Events Demo</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .event-box { 
                border: 1px solid #ccc; 
                height: 300px; 
                overflow-y: auto; 
                padding: 10px; 
                margin: 20px 0; 
                background: #f9f9f9;
            }
            .control-group { margin: 10px 0; }
            button { 
                padding: 8px 16px; 
                background: #007cba; 
                color: white; 
                border: none; 
                cursor: pointer; 
                margin-right: 10px;
            }
            button:hover { background: #005a87; }
            button:disabled { background: #ccc; cursor: not-allowed; }
            .status { 
                padding: 10px; 
                margin: 10px 0; 
                border-radius: 4px; 
            }
            .connected { background: #d4edda; color: #155724; }
            .disconnected { background: #f8d7da; color: #721c24; }
            .event-item {
                margin: 5px 0;
                padding: 8px;
                border-radius: 4px;
                border-left: 4px solid #007cba;
                background: white;
            }
            .event-time {
                font-size: 0.8em;
                color: #666;
                margin-bottom: 4px;
            }
            .event-data {
                font-family: monospace;
                background: #f5f5f5;
                padding: 4px;
                border-radius: 2px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Server-Sent Events Demo</h1>
            <div id="status" class="status disconnected">未连接</div>
            
            <div class="control-group">
                <button id="connectBtn" onclick="connect()">连接 SSE</button>
                <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
                <button onclick="requestWeather()">请求天气数据</button>
                <button onclick="requestNews()">请求新闻</button>
                <button onclick="startTimer()">开始计时器</button>
                <button onclick="stopTimer()">停止计时器</button>
            </div>
            
            <div class="event-box" id="events"></div>
            
            <div class="control-group">
                <h3>手动触发事件:</h3>
                <button onclick="triggerCustomEvent()">自定义事件</button>
                <button onclick="triggerError()">错误事件</button>
                <button onclick="clearEvents()">清空事件</button>
            </div>
        </div>

        <script>
            let eventSource = null;
            let timerActive = false;
            
            function addEvent(type, data, eventId = null) {
                const events = document.getElementById('events');
                const div = document.createElement('div');
                div.className = 'event-item';
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'event-time';
                timeDiv.textContent = new Date().toLocaleTimeString();
                
                const typeDiv = document.createElement('div');
                typeDiv.innerHTML = \`<strong>事件类型:</strong> \${type}\`;
                
                const dataDiv = document.createElement('div');
                dataDiv.className = 'event-data';
                dataDiv.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
                
                div.appendChild(timeDiv);
                div.appendChild(typeDiv);
                if (eventId) {
                    const idDiv = document.createElement('div');
                    idDiv.innerHTML = \`<strong>事件ID:</strong> \${eventId}\`;
                    div.appendChild(idDiv);
                }
                div.appendChild(dataDiv);
                
                events.appendChild(div);
                events.scrollTop = events.scrollHeight;
            }
            
            function updateStatus(connected) {
                const status = document.getElementById('status');
                const connectBtn = document.getElementById('connectBtn');
                const disconnectBtn = document.getElementById('disconnectBtn');
                
                if (connected) {
                    status.textContent = '已连接到 SSE';
                    status.className = 'status connected';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                } else {
                    status.textContent = '未连接';
                    status.className = 'status disconnected';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                }
            }
            
            function connect() {
                if (eventSource) {
                    addEvent('error', '已经连接了');
                    return;
                }
                
                eventSource = new EventSource('/events');
                
                eventSource.onopen = function() {
                    updateStatus(true);
                    addEvent('connection', '连接已建立');
                };
                
                eventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        addEvent('message', data, event.lastEventId);
                    } catch (e) {
                        addEvent('message', event.data, event.lastEventId);
                    }
                };
                
                // 自定义事件监听器
                eventSource.addEventListener('weather', function(event) {
                    const data = JSON.parse(event.data);
                    addEvent('weather', data, event.lastEventId);
                });
                
                eventSource.addEventListener('news', function(event) {
                    const data = JSON.parse(event.data);
                    addEvent('news', data, event.lastEventId);
                });
                
                eventSource.addEventListener('timer', function(event) {
                    const data = JSON.parse(event.data);
                    addEvent('timer', data, event.lastEventId);
                });
                
                eventSource.addEventListener('custom', function(event) {
                    const data = JSON.parse(event.data);
                    addEvent('custom', data, event.lastEventId);
                });
                
                eventSource.addEventListener('error_event', function(event) {
                    const data = JSON.parse(event.data);
                    addEvent('error_event', data, event.lastEventId);
                });
                
                eventSource.onerror = function(event) {
                    addEvent('error', '连接错误或中断');
                    updateStatus(false);
                    eventSource = null;
                };
            }
            
            function disconnect() {
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                    updateStatus(false);
                    addEvent('connection', '连接已断开');
                }
            }
            
            function requestWeather() {
                fetch('/trigger/weather', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        addEvent('request', '天气数据请求已发送');
                    })
                    .catch(error => {
                        addEvent('error', '请求失败: ' + error.message);
                    });
            }
            
            function requestNews() {
                fetch('/trigger/news', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        addEvent('request', '新闻数据请求已发送');
                    })
                    .catch(error => {
                        addEvent('error', '请求失败: ' + error.message);
                    });
            }
            
            function startTimer() {
                if (timerActive) {
                    addEvent('error', '计时器已经在运行');
                    return;
                }
                
                fetch('/trigger/timer/start', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        timerActive = true;
                        addEvent('request', '计时器已启动');
                    })
                    .catch(error => {
                        addEvent('error', '启动计时器失败: ' + error.message);
                    });
            }
            
            function stopTimer() {
                fetch('/trigger/timer/stop', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        timerActive = false;
                        addEvent('request', '计时器已停止');
                    })
                    .catch(error => {
                        addEvent('error', '停止计时器失败: ' + error.message);
                    });
            }
            
            function triggerCustomEvent() {
                fetch('/trigger/custom', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        addEvent('request', '自定义事件已触发');
                    })
                    .catch(error => {
                        addEvent('error', '触发事件失败: ' + error.message);
                    });
            }
            
            function triggerError() {
                fetch('/trigger/error', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        addEvent('request', '错误事件已触发');
                    })
                    .catch(error => {
                        addEvent('error', '触发错误事件失败: ' + error.message);
                    });
            }
            
            function clearEvents() {
                document.getElementById('events').innerHTML = '';
            }
            
            // 页面加载时自动连接
            window.onload = function() {
                connect();
            };
            
            // 页面卸载时断开连接
            window.onbeforeunload = function() {
                if (eventSource) {
                    eventSource.close();
                }
            };
        </script>
    </body>
    </html>
  `);
});

// SSE 端点
app.get('/events', (req: Request, res: Response) => {
  console.log('新的 SSE 连接:', req.ip);

  // 设置 SSE 头部
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });

  // 添加到客户端列表
  sseClients.add(res);

  // 发送欢迎消息
  const welcomeData = {
    type: 'welcome',
    message: '欢迎连接到 SSE 服务器!',
    timestamp: new Date().toISOString(),
    clientCount: sseClients.size
  };

  res.write(`id: ${Date.now()}\n`);
  res.write(`event: message\n`);
  res.write(`data: ${JSON.stringify(welcomeData)}\n\n`);

  // 连接关闭处理
  req.on('close', () => {
    console.log('SSE 连接关闭');
    sseClients.delete(res);
  });

  req.on('error', (error) => {
    console.error('SSE 连接错误:', error);
    sseClients.delete(res);
  });
});

// 触发天气事件
app.post('/trigger/weather', (_req: Request, res: Response) => {
  const weatherData = {
    city: 'Beijing',
    temperature: Math.floor(Math.random() * 30) + 5,
    condition: ['晴天', '多云', '小雨', '阴天'][Math.floor(Math.random() * 4)],
    humidity: Math.floor(Math.random() * 40) + 40,
    windSpeed: Math.floor(Math.random() * 20) + 5,
    timestamp: new Date().toISOString()
  };

  broadcastEvent('weather', weatherData);
  res.json({ success: true, message: '天气事件已发送' });
});

// 触发新闻事件
app.post('/trigger/news', (_req: Request, res: Response) => {
  const newsData = {
    title: '今日科技新闻',
    articles: [
      { title: 'AI 技术新突破', summary: '人工智能在多个领域取得重大进展' },
      { title: '新型编程语言发布', summary: '更高效的开发工具问世' },
      { title: '云计算市场增长', summary: '企业数字化转型加速' }
    ],
    timestamp: new Date().toISOString()
  };

  broadcastEvent('news', newsData);
  res.json({ success: true, message: '新闻事件已发送' });
});

// 计时器相关变量
let timerInterval: NodeJS.Timeout | null = null;
let timerCount = 0;

// 启动计时器
app.post('/trigger/timer/start', (_req: Request, res: Response) => {
  if (timerInterval) {
    res.json({ success: false, message: '计时器已在运行' });
    return;
  }

  timerCount = 0;
  timerInterval = setInterval(() => {
    timerCount++;
    const timerData = {
      count: timerCount,
      message: `计时器: ${timerCount} 秒`,
      timestamp: new Date().toISOString()
    };

    broadcastEvent('timer', timerData);
  }, 1000);

  res.json({ success: true, message: '计时器已启动' });
});

// 停止计时器
app.post('/trigger/timer/stop', (_req: Request, res: Response) => {
  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
    
    const stopData = {
      message: '计时器已停止',
      finalCount: timerCount,
      timestamp: new Date().toISOString()
    };

    broadcastEvent('timer', stopData);
  }

  res.json({ success: true, message: '计时器已停止' });
});

// 触发自定义事件
app.post('/trigger/custom', (_req: Request, res: Response) => {
  const customData = {
    message: '这是一个自定义事件',
    randomNumber: Math.floor(Math.random() * 1000),
    data: {
      key1: 'value1',
      key2: 'value2',
      nested: {
        prop: 'nested value'
      }
    },
    timestamp: new Date().toISOString()
  };

  broadcastEvent('custom', customData);
  res.json({ success: true, message: '自定义事件已发送' });
});

// 触发错误事件
app.post('/trigger/error', (_req: Request, res: Response) => {
  const errorData = {
    error: 'DEMO_ERROR',
    message: '这是一个演示错误事件',
    code: 500,
    details: '用于测试错误处理',
    timestamp: new Date().toISOString()
  };

  broadcastEvent('error_event', errorData);
  res.json({ success: true, message: '错误事件已发送' });
});

// 广播事件到所有客户端
function broadcastEvent(eventType: string, data: any) {
  const eventId = Date.now().toString();
  const message = `id: ${eventId}\nevent: ${eventType}\ndata: ${JSON.stringify(data)}\n\n`;

  sseClients.forEach(client => {
    try {
      client.write(message);
    } catch (error) {
      console.error('发送 SSE 消息错误:', error);
      sseClients.delete(client);
    }
  });

  console.log(`广播事件 ${eventType} 给 ${sseClients.size} 个客户端`);
}

// 定期发送服务器状态
setInterval(() => {
  const statusData = {
    type: 'server_status',
    clientCount: sseClients.size,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  };

  broadcastEvent('message', statusData);
}, 30000); // 每30秒发送一次

const PORT = process.env.SSE_PORT || 3090;
app.listen(PORT, () => {
  console.log(`SSE 服务器运行在端口 ${PORT}`);
  console.log(`访问 http://localhost:${PORT} 查看演示页面`);
});
