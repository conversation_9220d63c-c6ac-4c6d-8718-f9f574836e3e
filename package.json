{"name": "weather-mcp-server", "version": "0.1.0", "type": "module", "bin": {"weather-mcp-server": "dist/index.js"}, "scripts": {"build": "tsc && shx chmod +x dist/index.js", "watch": "tsc --watch", "start": "node ./dist/index.js", "start:streamableHttp": "node dist/streamableHttp.js"}, "files": ["dist"], "keywords": ["mcp"], "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "description": "Weather MCP Server", "dependencies": {"@modelcontextprotocol/sdk": "^1.10.1", "axios": "^1.10.0", "express": "^5.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "^22.13.10", "shx": "^0.3.4", "typescript": "^5.8.2"}}