# SSR (Server-Side Rendering) Demo

这是一个完整的服务器端渲染演示，展示了如何在 Node.js 中实现 SSR 功能。

## 功能特性

### 🚀 核心功能
- **服务器端渲染**: 在服务器端生成完整的 HTML 页面
- **路由系统**: 支持静态和动态路由匹配
- **数据获取**: 模拟异步数据获取和处理
- **组件渲染**: 基于组件的页面渲染系统
- **性能优化**: 渲染时间监控和性能头部

### 📱 支持的页面
- **首页** (`/`) - 展示网站统计信息
- **关于我们** (`/about`) - 公司信息和团队介绍
- **用户资料** (`/user/:id`) - 动态用户资料页面
- **产品列表** (`/products`) - 产品展示页面
- **产品详情** (`/products/:id`) - 单个产品详情页面
- **404页面** - 未找到页面的处理

### 🎨 UI 特性
- **响应式设计**: 适配不同屏幕尺寸
- **现代样式**: 使用现代 CSS 设计
- **用户体验**: 清晰的导航和信息展示
- **错误处理**: 友好的错误页面

## 技术实现

### 架构设计
```
src/
├── entry-server.ts    # SSR 渲染入口
├── ssrServer.ts       # SSR 服务器实现（基于 Node.js VM）
└── ...
```

### 核心组件

#### 1. 渲染入口 (`entry-server.ts`)
- **路由匹配**: 支持静态和动态路由
- **数据获取**: 异步数据获取模拟
- **组件渲染**: HTML 字符串生成
- **错误处理**: 完整的错误处理机制

#### 2. SSR 服务器 (`ssrServer.ts`)
- **HTTP 服务器**: 基于 Node.js 原生 HTTP 模块
- **VM 渲染池**: 使用 Node.js VM 模块创建隔离的执行环境
- **代码转换**: 自动将 ES6 模块转换为 CommonJS
- **上下文管理**: 请求上下文处理和轮询分配
- **性能监控**: 渲染时间统计和 VM 状态监控

### VM 沙箱特性

#### 🔒 安全隔离
- **内存隔离**: 每个 VM 上下文独立运行
- **API 限制**: 只暴露必要的 Node.js API
- **超时控制**: 防止无限循环和长时间执行
- **资源限制**: 控制内存和 CPU 使用

#### ⚡ 性能优化
- **渲染池**: 4 个预创建的 VM 上下文轮询使用
- **代码预编译**: 启动时预编译渲染代码
- **上下文复用**: 避免重复创建 VM 上下文的开销
- **并发处理**: 支持多个请求同时渲染

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 编译项目
```bash
npm run build
```

### 3. 启动 SSR 服务器
```bash
npm run start:ssr
```

服务器将在 http://localhost:3091 启动

### 4. 访问演示
- **首页**: http://localhost:3091/
- **关于我们**: http://localhost:3091/about
- **用户资料**: http://localhost:3091/user/123
- **产品列表**: http://localhost:3091/products
- **产品详情**: http://localhost:3091/products/1
- **健康检查**: http://localhost:3091/health
- **渲染器状态**: http://localhost:3091/renderer-status

## API 接口

### 健康检查
```
GET /health
```
返回服务器状态信息：
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456,
  "renderer": {
    "poolSize": 4,
    "contextsCreated": 4,
    "currentIndex": 8,
    "totalRenders": 8
  }
}
```

### 渲染器状态
```
GET /renderer-status
```
返回详细的渲染器和系统状态：
```json
{
  "renderer": {
    "poolSize": 4,
    "contextsCreated": 4,
    "currentIndex": 8,
    "totalRenders": 8
  },
  "memory": {
    "rss": 45678912,
    "heapTotal": 12345678,
    "heapUsed": 8765432,
    "external": 1234567
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 页面渲染
所有其他路由都会触发 SSR 渲染，返回完整的 HTML 页面。

## 渲染流程

1. **请求接收**: HTTP 服务器接收请求
2. **上下文构建**: 解析请求头、Cookie、查询参数等
3. **VM 上下文选择**: 从渲染池中轮询选择一个可用的 VM 上下文
4. **代码执行**: 在隔离的 VM 环境中执行渲染逻辑
5. **路由匹配**: 根据 URL 匹配对应的组件
6. **数据获取**: 异步获取页面所需数据
7. **组件渲染**: 生成 HTML 字符串
8. **响应返回**: 返回完整的 HTML 页面

## VM 实现详解

### 🏗️ VM 上下文创建
```javascript
// 创建沙箱环境
const sandbox = {
  console: { log, error, warn, info },  // 受控的控制台输出
  Date, Math, JSON, Promise,            // 基础 JavaScript 对象
  setTimeout, setInterval,              // 定时器（受控）
  Buffer, process: { env, version },    // 必要的 Node.js API
  require, module, exports,             // 模块系统
  __ssrRender: null                     // 渲染函数存储
};

// 创建 VM 上下文
const context = vm.createContext(sandbox, {
  name: 'SSR-Context',
  codeGeneration: { strings: false, wasm: false }
});
```

### 🔄 代码转换过程
1. **读取源码**: 从编译后的 `entry-server.js` 读取代码
2. **ES6 → CommonJS**: 将 `export` 转换为 CommonJS 格式
3. **移除 import**: 清理不需要的 import 语句
4. **函数包装**: 确保 render 函数可以被访问
5. **代码执行**: 在 VM 上下文中执行转换后的代码

### ⚖️ 负载均衡
- **轮询算法**: 请求按顺序分配给不同的 VM 上下文
- **并发处理**: 多个请求可以同时在不同的 VM 中执行
- **状态隔离**: 每个 VM 上下文完全独立，避免状态污染

## 性能特性

### 渲染监控
每个请求都会记录：
- 渲染开始时间
- 渲染完成时间
- 渲染耗时（毫秒）

### 响应头部
```
x-rendered-by: ssr-server
x-render-time: 2024-01-01T00:00:00.000Z
x-render-time-ms: 15
x-server-time: 2024-01-01T00:00:00.000Z
```

## 错误处理

### 渲染错误
- 自动捕获渲染过程中的错误
- 返回友好的错误页面
- 记录详细的错误日志

### 404 处理
- 未匹配路由自动返回 404 页面
- 保持一致的页面样式和导航

## 扩展功能

### 数据层集成
可以轻松集成：
- 数据库查询
- API 调用
- 缓存系统
- 认证授权

### 前端框架集成
支持集成：
- React SSR
- Vue SSR
- Angular Universal
- Svelte Kit

### 部署优化
- Docker 容器化
- PM2 进程管理
- Nginx 反向代理
- CDN 静态资源

## 开发建议

### 1. 组件开发
- 保持组件的纯函数特性
- 避免在渲染过程中产生副作用
- 合理处理异步数据获取

### 2. 性能优化
- 实现组件级缓存
- 优化数据获取逻辑
- 使用流式渲染

### 3. 错误处理
- 实现完整的错误边界
- 提供降级渲染方案
- 记录详细的错误信息

## 与其他 Demo 的对比

| 特性 | WebSocket | SSE | SSR |
|------|-----------|-----|-----|
| 通信方式 | 双向实时 | 单向推送 | 请求-响应 |
| 使用场景 | 实时交互 | 数据推送 | 页面渲染 |
| 复杂度 | 中等 | 简单 | 高 |
| 性能 | 高 | 中等 | 取决于实现 |

## 故障排除

### 常见问题

1. **模块加载失败**
   - 确保 TypeScript 编译成功
   - 检查文件路径是否正确

2. **渲染超时**
   - 检查数据获取逻辑
   - 优化异步操作

3. **内存泄漏**
   - 避免在渲染过程中创建定时器
   - 正确处理异步资源清理

### 调试技巧
- 查看控制台日志
- 使用浏览器开发者工具
- 检查网络请求和响应

## 总结

这个 SSR Demo 展示了：
- 完整的服务器端渲染实现
- 现代化的架构设计
- 丰富的功能特性
- 良好的错误处理
- 性能监控和优化

它为构建生产级的 SSR 应用提供了一个很好的起点和参考。
