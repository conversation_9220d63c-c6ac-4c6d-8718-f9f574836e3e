# WebSocket 和 SSE Demo

这个项目包含了两个实时通信的演示：WebSocket 和 Server-Sent Events (SSE)。

## 功能特性

### WebSocket Demo (`src/websocket.ts`)
- 双向实时通信
- 心跳机制
- 消息广播
- 模拟天气数据获取
- 客户端连接管理
- 内置HTML测试页面

### SSE Demo (`src/sse.ts`)
- 单向服务器推送
- 多种事件类型支持
- 实时计时器
- 天气和新闻数据推送
- 自定义事件触发
- 内置HTML测试页面

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 编译项目
```bash
npm run build
```

### 3. 运行 WebSocket Demo
```bash
npm run start:websocket
```
然后访问 http://localhost:3089 查看演示页面

### 4. 运行 SSE Demo
```bash
npm run start:sse
```
然后访问 http://localhost:3090 查看演示页面

## WebSocket Demo 功能

### 客户端功能
- **连接/断开连接**: 手动控制WebSocket连接
- **发送消息**: 发送文本消息并广播给其他客户端
- **获取天气信息**: 请求模拟的天气数据
- **心跳机制**: 每5秒发送一次ping消息保持连接

### 服务器功能
- **消息处理**: 处理不同类型的消息（ping、weather、message等）
- **广播**: 将消息转发给所有连接的客户端
- **状态推送**: 定期发送服务器状态信息
- **连接管理**: 自动清理断开的连接

### 消息格式
```json
{
  "type": "message|ping|weather|broadcast",
  "content": "消息内容",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## SSE Demo 功能

### 客户端功能
- **连接管理**: 自动连接和重连机制
- **事件监听**: 监听多种自定义事件类型
- **手动触发**: 通过按钮触发各种服务器事件
- **实时显示**: 实时显示接收到的事件数据

### 服务器功能
- **多事件类型**: 支持weather、news、timer、custom等事件
- **计时器**: 可启动/停止的实时计时器
- **数据模拟**: 模拟天气和新闻数据
- **状态推送**: 定期推送服务器状态

### 支持的事件类型
- `message`: 普通消息
- `weather`: 天气数据
- `news`: 新闻数据  
- `timer`: 计时器事件
- `custom`: 自定义事件
- `error_event`: 错误事件

### API 端点
- `GET /events`: SSE连接端点
- `POST /trigger/weather`: 触发天气事件
- `POST /trigger/news`: 触发新闻事件
- `POST /trigger/timer/start`: 启动计时器
- `POST /trigger/timer/stop`: 停止计时器
- `POST /trigger/custom`: 触发自定义事件
- `POST /trigger/error`: 触发错误事件

## 技术栈

- **Node.js**: 运行时环境
- **TypeScript**: 编程语言
- **Express**: Web框架
- **ws**: WebSocket库
- **HTML/CSS/JavaScript**: 前端演示页面

## 项目结构

```
src/
├── websocket.ts    # WebSocket服务器和演示页面
├── sse.ts         # SSE服务器和演示页面
├── server.ts      # 原有的MCP服务器
├── index.ts       # 原有的入口文件
└── streamableHttp.ts # 原有的HTTP服务器
```

## 使用场景

### WebSocket 适用于：
- 实时聊天应用
- 在线游戏
- 协作编辑
- 实时数据同步
- 需要双向通信的场景

### SSE 适用于：
- 实时通知推送
- 股票价格更新
- 新闻推送
- 系统状态监控
- 单向数据流场景

## 注意事项

1. **端口配置**: WebSocket使用3089端口，SSE使用3090端口
2. **浏览器兼容性**: 现代浏览器都支持WebSocket和SSE
3. **连接管理**: 页面刷新或关闭时会自动断开连接
4. **错误处理**: 两个demo都包含了完整的错误处理机制
5. **性能考虑**: 生产环境中需要考虑连接数限制和资源管理

## 扩展建议

1. **认证授权**: 添加用户认证机制
2. **消息持久化**: 将消息存储到数据库
3. **负载均衡**: 支持多实例部署
4. **监控日志**: 添加详细的日志和监控
5. **安全加固**: 添加CORS、限流等安全措施
